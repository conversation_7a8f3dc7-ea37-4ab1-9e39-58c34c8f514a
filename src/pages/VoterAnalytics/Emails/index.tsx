import cn from "classnames";
import { motion } from "framer-motion";

import * as interfaces from 'interfaces';
import PageLayout from 'shared/PageLayout';
import { AuthService, StatService } from "services";
import { useServiceContext } from "services/ServiceProvider";

import classes from './Emails.module.scss';
import sharedClasses from '../Shared.module.scss';
import Filter from 'components/VoterAnalytics/Filter';
import AnalyticsButton from 'components/VoterAnalytics/Buttons';
import StatusOptions from "components/StatusOptions";
import FilterBar from "components/VoterAnalytics/FilterBar";
import { useFilterStore } from "hooks/zustand/filterStore";
import { Chart } from "components/VoterAnalytics/Chart";
import { EmailTable } from "components/VoterAnalytics/Table";
import PortalPopup from "components/PortalPopup";
import EmailDetails from "components/VoterAnalytics/EmailDetails";
import { useEffect, useMemo, useRef, useState } from "react";
import { Popover, PopoverContent, PopoverTrigger } from "shared/ui/popover";
import ExportButton from "components/VoterAnalytics/ExportElementBtn";
import { processChartData } from 'utils/stats';
import Loader from 'components/Loader';
import xlsx from "json-as-xlsx";
import { useUIStore } from "hooks/zustand/uiStore";
import useExportPdf from "hooks/useExportPdf.hook";
import {
  useStats,
  useAnalytics,
  useEmailAnalytics,
  useEmailChartData,
  useEmailBulkSendData
} from 'hooks/reactQuery/useAnalyticsQueries';

export default function Emails(props: { client: interfaces.ClientInterface; service: StatService; authService: AuthService; user?: interfaces.UserInterface; }) {
  const { client, service, authService, user } = props;
  const { adminStatsService } = useServiceContext();
  const filterStore = useFilterStore();
  const [analytics, setAnalytics] = useState<any>([]);
  const [emailAnalytics, setEmailAnalytics] = useState<any>([]);
  const [emailBulkSendData, setEmailBulkSendData] = useState<any>([]);
  const [stats, setStats] = useState<any>([]);
  const uiStore = useUIStore();
  const { uiType, setUiType } = uiStore;
  const { exportPDF } = useExportPdf(setUiType);

  // Update handler to forward parameters
  const exportPDFHandler = (exportEngagements: boolean, exportVideos: boolean, exportEmails: boolean) => {
    exportPDF(exportEngagements, exportVideos, exportEmails, loading);
    adminStatsService?.trackEvent('EmailsPage', 'export_pdf');
  };

  const chartRef = useRef(null);

  // Fetch stats
  const statsQuery = useStats(service);

  // Fetch analytics data
  const analyticsQuery = useAnalytics(
    service,
    filterStore.timeFilter,
    filterStore.locationFilters,
    user?.accessLevel === "super admin",
    client?.id
  );

  // Fetch email analytics
  const emailAnalyticsQuery = useEmailAnalytics(
    service,
    filterStore.timeFilter,
    filterStore.locationFilters,
    user?.accessLevel === "super admin",
    client?.id
  );

  // Fetch email chart data
  const emailChartDataQuery = useEmailChartData(
    service,
    filterStore.timeFilter,
    filterStore.locationFilters,
    user?.accessLevel === "super admin",
    client?.id
  );

  // Fetch email bulk send data
  const emailBulkSendDataQuery = useEmailBulkSendData(
    service,
    filterStore.timeFilter,
    filterStore.locationFilters,
    user?.accessLevel === "super admin",
    client?.id
  );

  // Check if email-related data is empty and refetch if needed
  useEffect(() => {
    const hasEmptyAnalytics = !service.analytics || service.analytics.length === 0;
    const hasEmptyEmailAnalytics = !service.emailAnalytics ||
      (service.emailAnalytics.opens === 0 && service.emailAnalytics.clicks === 0 && service.emailAnalytics.deliveries === 0);
    const hasEmptyEmailChartData = !service.emailAnalytics?.chartData || service.emailAnalytics.chartData.length === 0;
    const hasEmptyEmailBulkSendData = !service.emailAnalytics?.bulkSendData?.bulkSends ||
      service.emailAnalytics.bulkSendData.bulkSends.length === 0;

    if (hasEmptyAnalytics || hasEmptyEmailAnalytics || hasEmptyEmailChartData || hasEmptyEmailBulkSendData) {
      // Refetch email-related data when page opens with empty data
      if (hasEmptyAnalytics) {
        analyticsQuery.refetch();
      }
      if (hasEmptyEmailAnalytics) {
        emailAnalyticsQuery.refetch();
      }
      if (hasEmptyEmailChartData) {
        emailChartDataQuery.refetch();
      }
      if (hasEmptyEmailBulkSendData) {
        emailBulkSendDataQuery.refetch();
      }
    }
  }, []); // Empty dependency array to run only on mount

  // Set loading state based on all queries
  const loading =
    statsQuery.isLoading ||
    analyticsQuery.isLoading ||
    emailAnalyticsQuery.isLoading ||
    emailChartDataQuery.isLoading ||
    emailBulkSendDataQuery.isLoading;

  // Update state when stats data is available
  useMemo(() => {
    if (statsQuery.data) {
      setStats(statsQuery.data);
    }
  }, [statsQuery.data]);

  // Update state when analytics data is available and update service properties
  useEffect(() => {
    if (analyticsQuery.data) {
      // Update service properties with the analytics data
      service.analytics = analyticsQuery.data.analytics;
      service.mobileDesktop = analyticsQuery.data.mobileDesktop;
      service.referralPercentage = analyticsQuery.data.referralPercentage;
      service.engagementsChart = analyticsQuery.data.engagementsChart;
      service.sentimentAnalytics = analyticsQuery.data.sentiments;
      service.aiQueriesChart = analyticsQuery.data.aiQueriesChart;
      service.aiQueriesSentiments = analyticsQuery.data.aiQueriesSentiments;

      // Recompute values to update service.computedValues
      service.computeValues();

      // Update local state
      setAnalytics(analyticsQuery.data.analytics);
    }
  }, [analyticsQuery.data, service]);

  // Update state when email analytics data is available and update service properties
  useMemo(() => {
    if (emailAnalyticsQuery.data) {
      // Update service properties with the email analytics data
      service.emailAnalytics = {
        ...service.emailAnalytics,
        opens: Number(emailAnalyticsQuery.data.opens),
        clicks: Number(emailAnalyticsQuery.data.clicks),
        deliveries: Number(emailAnalyticsQuery.data.deliveries)
      };

      // Recompute email values to update service.computedValues
      service.computeEmailValues();

      // Update local state
      setEmailAnalytics(emailAnalyticsQuery.data);
    }
  }, [emailAnalyticsQuery.data, service, filterStore.timeFilter]);

  // Update state when email bulk send data is available and update service properties
  useMemo(() => {
    if (emailBulkSendDataQuery.data) {
      // Update service properties with the email bulk send data
      service.emailAnalytics.bulkSendData = emailBulkSendDataQuery.data;
      service.videoPlayStats = emailBulkSendDataQuery.data.videoPlayStats || [];

      // Recompute quartile data and bulk send data to update service.computedValues
      // Only compute quartile data if videoPlayStats is available
      if (service.videoPlayStats && service.videoPlayStats.length > 0) {
        service.computeQuartileData();
      }

      // Only compute email bulk send data if bulkSends is available
      if (emailBulkSendDataQuery.data.bulkSends && Array.isArray(emailBulkSendDataQuery.data.bulkSends)) {
        service.computeEmailBulkSendData();
      }

      // Update local state
      setEmailBulkSendData(emailBulkSendDataQuery.data);
    }
  }, [emailBulkSendDataQuery.data, service, filterStore.timeFilter]);

  const rawChartData = service.emailAnalytics.chartData;

  const processedChartData = useMemo(() => processChartData(rawChartData, 'opens', 'deliveries'), [rawChartData]);

  // Create a derived value that depends on local state to ensure React re-renders
  const bulkSendTableData = useMemo(() => {
    return service.computedValues.bulkSendData;
  }, [emailBulkSendData, service.computedValues.bulkSendData]);

  const [isEmailDetailsOpen, setEmailDetailsOpen] = useState(false);
  const [dataIndex, setDataIndex] = useState(0);

  const tableItemClickHandler = (index: number) => {
    setEmailDetailsOpen(true);
    setDataIndex(index);

    const emailData = service.computedValues.bulkSendData?.[index];
    if (emailData) {
      adminStatsService?.trackEvent('EmailsPage', 'open_email_details');
    }
  }

  const emailsCsv = useMemo(() => {
    return service.getEmailsCsv();
  }, [service, analytics, emailBulkSendData]);

  const xlsxSettings = {
    fileName: 'emails'
  }

  const downloadExcelHandler = () => {
    const data = service.getEmailsXlsx();
    // @ts-ignore
    xlsx(data, xlsxSettings);
    adminStatsService?.trackEvent('EmailsPage', 'export_excel');
  }

  return (
    <>
      <PageLayout user={user} client={client} authService={authService} className={cn(classes.wrapper)}>
        {loading ? (
          <Loader customId='loader' />
        ) : (
          <main className={classes.mainContainer}>
            <section className={classes.contentWrapper}>
              <div className={classes.contentPanel}>
                <div className={classes.filterContainer}>
                  <div className={uiType !== "pdf" ? "flex-1" : ""}>
                    {uiType === "pdf" && <h2 className={"w-full"}>{client.name} Report</h2>}
                    {uiType === "pdf" && <h3 className={"text-2xl mb-5"}>Date: {filterStore.timeFilter?.name || 'Default - All Time'}</h3>}
                    <h1 className={classes.pageTitle}>Emails</h1>
                  </div>
                  {/* {uiType !== "pdf" && <>
                    <Filter isSuperAdmin={user?.accessLevel === "super admin"} type="Default" />
                    <AnalyticsButton
                      onClickPDF={exportPDFHandler} onClickExcel={downloadExcelHandler} csvData={emailsCsv || ""} csvFilename="emails.csv"
                    />
                  </>} */}
                </div>
                {uiType !== "pdf" && <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.4, delay: 0.1 }}
                  className={cn(classes.filterBarContainer, classes.hideOnMobile)}>
                  <FilterBar />
                </motion.div>}
                {/* TODO: Uncomment */}
                {/* <div className={sharedClasses.metricContainer}>
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.15 }}
                    className={sharedClasses.statItemContainer}>
                    <img
                      className={sharedClasses.statIcon}
                      loading="lazy"
                      alt=""
                      src="/analytics/svg-icons/inbox.svg"
                    />
                    <div className={sharedClasses.metricLabelsContainer}>
                      <div className={sharedClasses.totalTraffic}>Delivered</div>
                      <b className={sharedClasses.metricDurations}>{service.computedValues.deliveredEmails.toLocaleString()}</b>
                    </div>
                  </motion.div>
                  <img
                    className={cn(sharedClasses.metricDivider, sharedClasses.hideOnMobile)}
                    alt=""
                    src="/analytics/chart/vertical-divider.svg"
                  />
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    className={sharedClasses.statItemContainer}>
                    <img
                      className={sharedClasses.statIcon}
                      loading="lazy"
                      alt=""
                      src="/analytics/svg-icons/open-mail-large.svg"
                    />
                    <div className={sharedClasses.metricLabelsContainer}>
                      <div className={sharedClasses.engagementRate}>Opened</div>
                      <b className={sharedClasses.metricValue}>
                        <span>{service.computedValues.openedEmailsPercentage.toFixed(0)}</span>
                        <span className={sharedClasses.metricUnit}>%</span>
                      </b>
                    </div>
                  </motion.div>
                  <img
                    className={cn(sharedClasses.metricDivider, sharedClasses.hideOnMobile)}
                    alt=""
                    src="/analytics/chart/vertical-divider.svg"
                  />
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.45 }}
                    className={sharedClasses.statItemContainer}>
                    <img
                      className={sharedClasses.statIcon}
                      loading="lazy"
                      alt=""
                      src="/analytics/svg-icons/view.svg"
                    />
                    <div className={sharedClasses.metricLabelsContainer}>
                      <div className={sharedClasses.engagementRate}>Clicked</div>
                      <b className={sharedClasses.metricValue}>
                        <span>{service.computedValues.clickedEmailsPercentage.toFixed(0)}</span>
                        <span className={sharedClasses.metricUnit}>%</span>
                      </b>
                    </div>
                  </motion.div>
                </div> */}
                {/* <Popover>
                  <motion.div
                    initial={{ opacity: 0, x: 40 }}
                    animate={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.4, delay: 0.4 }}
                    className={cn(classes.chartContainer, 'chartContainer')}>
                    <div className={classes.chartHeaderContainer}>
                      <div className={classes.chartTitleContainer}>
                        <h3 className={classes.chartTitle}>Open Rate</h3>
                      </div>
                      <PopoverTrigger asChild>
                        <img
                          className={classes.moreInfoIcon}
                          loading="lazy"
                          alt=""
                          src="/analytics/icons/more-info-icon.png"
                        />
                      </PopoverTrigger>
                      <PopoverContent className='w-fit'>
                        <ExportButton mainContent={chartRef} />
                      </PopoverContent>
                    </div>
                    <div className={classes.chartContent}>
                      <Chart type="emails" color="#C169D7" chartData={processedChartData} chartRef={chartRef} />
                    </div>
                  </motion.div>
                </Popover> */}
                <motion.div
                  initial={{ opacity: 0, x: 40 }}
                  animate={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.4, delay: 0.6 }}
                  className={classes.tableContainer}>
                  <EmailTable openDetailsModal={tableItemClickHandler} data={bulkSendTableData} />
                </motion.div>
              </div>
            </section>
          </main>
        )}
      </PageLayout>
      {isEmailDetailsOpen && (
        <PortalPopup
          overlayColor="rgba(113, 113, 113, 0.3)"
          placement="Centered"
          onOutsideClick={() => setEmailDetailsOpen(false)}
        >
          <EmailDetails onClose={() => setEmailDetailsOpen(false)} data={{
            ...service.computedValues.bulkSendData?.[dataIndex],
            date: service.computedValues.bulkSendData[dataIndex]?.created_at,
            opensPercentage: service.computedValues?.bulkSendData[dataIndex]?.opensPercentage,
            clicksPercentage: service.computedValues?.bulkSendData[dataIndex]?.clicksPercentage,
            blocksPercentage: service.computedValues?.bulkSendData[dataIndex]?.blocksPercentage,
          }} />
        </PortalPopup>
      )}
    </>
  )
}