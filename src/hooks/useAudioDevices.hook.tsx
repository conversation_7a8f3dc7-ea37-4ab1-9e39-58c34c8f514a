import { useState, useEffect, useCallback, useMemo } from 'react';

interface AudioDevice {
  deviceId: string;
  label: string;
}

export const useAudioDevices = () => {
  const [audioDevices, setAudioDevices] = useState<AudioDevice[]>([]);
  const [selectedDeviceId, setSelectedDeviceId] = useState<string | null>(
    () => localStorage.getItem('selectedAudioDevice')
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const getAudioDevices = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Step 1: Request basic permissions first
      let stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop());
      
      // Step 2: Wait for device enumeration to settle
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Step 3: Enumerate all devices
      let devices = await navigator.mediaDevices.enumerateDevices();
      console.log('All devices after basic permission:', devices);
      
      // Step 4: Try to access each audio input device individually to trigger detection
      const audioInputs = devices.filter(d => d.kind === 'audioinput');
      console.log('Initial audio inputs found:', audioInputs);
      
      // Step 5: Try different audio constraints that might trigger USB device detection
      const constraints = [
        { audio: { sampleRate: 44100 } },
        { audio: { sampleRate: 48000 } },
        { audio: { channelCount: 1 } },
        { audio: { channelCount: 2 } },
        { audio: { latency: 0 } },
        { audio: { 
          echoCancellation: false,
          noiseSuppression: false,
          autoGainControl: false,
          sampleRate: 48000,
          channelCount: 2
        }}
      ];
      
      for (const constraint of constraints) {
        try {
          console.log('Trying constraint:', constraint);
          stream = await navigator.mediaDevices.getUserMedia(constraint);
          stream.getTracks().forEach(track => track.stop());
          
          // Re-enumerate after each attempt
          const newDevices = await navigator.mediaDevices.enumerateDevices();
          const newAudioInputs = newDevices.filter(d => d.kind === 'audioinput');
          
          if (newAudioInputs.length > audioInputs.length) {
            console.log('Found more devices with constraint:', constraint);
            devices = newDevices;
            break;
          }
        } catch (err) {
          console.log('Constraint failed:', constraint, err);
        }
      }
      
      // Step 6: Final enumeration
      devices = await navigator.mediaDevices.enumerateDevices();
      console.log('Final device enumeration:', devices);
      
      const processedDevices = devices
        .filter((device) => device.kind === 'audioinput')
        .map((device) => {
          console.log('Processing device:', {
            deviceId: device.deviceId,
            label: device.label,
            groupId: device.groupId
          });
          return {
            deviceId: device.deviceId,
            label: device.label || `Microphone ${device.deviceId.slice(-4)}`,
          };
        });
      
      console.log('Final processed devices:', processedDevices);
      setAudioDevices(processedDevices);

      // Check if Rode appears in system audio devices
      navigator.mediaDevices.enumerateDevices().then(devices => {
        console.log('All devices:', devices);
        console.log('Audio inputs:', devices.filter(d => d.kind === 'audioinput'));
      })
    } catch (err) {
      console.error('Audio device enumeration error:', err);
      setError('Could not access audio devices. Please check permissions.');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    getAudioDevices();
    navigator.mediaDevices.addEventListener('devicechange', getAudioDevices);
    return () => {
      navigator.mediaDevices.removeEventListener('devicechange', getAudioDevices);
    };
  }, [getAudioDevices]);

  useEffect(() => {
    if (audioDevices.length > 0) {
      const isValidSelection = audioDevices.some(d => d.deviceId === selectedDeviceId);
      if (!selectedDeviceId || !isValidSelection) {
        const newDeviceId = audioDevices[0].deviceId;
        setSelectedDeviceId(newDeviceId);
      }
    }
  }, [audioDevices, selectedDeviceId]);

  useEffect(() => {
    if (selectedDeviceId) {
      localStorage.setItem('selectedAudioDevice', selectedDeviceId);
    }
  }, [selectedDeviceId]);

  const selectDevice = (deviceId: string) => {
    setSelectedDeviceId(deviceId);
  };

  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);

  const audioConstraints = useMemo(() => {
    if (isIOS) {
      return true; // Let iOS pick the mic
    }
    if (!selectedDeviceId) return true;
    return {
      deviceId: { exact: selectedDeviceId },
      echoCancellation: false,
      noiseSuppression: false,
      autoGainControl: false
    };
  }, [selectedDeviceId]);

  return {
    audioDevices,
    selectedDeviceId,
    selectDevice,
    isLoading,
    error,
    audioConstraints,
  };
};
