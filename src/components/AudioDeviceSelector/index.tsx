import React, { useState } from 'react';
import { Select } from '../Select';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faMicrophone, faExclamationTriangle } from '@fortawesome/free-solid-svg-icons';

interface AudioDevice {
  deviceId: string;
  label: string;
}

interface AudioDeviceSelectorProps {
  className?: string;
  audioDevices: AudioDevice[];
  selectedDeviceId: string | null;
  selectDevice: (deviceId: string) => void;
  isLoading: boolean;
  error: string | null;
}

export const AudioDeviceSelector: React.FC<AudioDeviceSelectorProps> = ({ 
  className = '',
  audioDevices,
  selectedDeviceId,
  selectDevice,
  isLoading,
  error,
}) => {
  const [enabled, setEnabled] = useState(false);
  const selectedDevice = audioDevices.find(device => device.deviceId === selectedDeviceId);

  const handleEnableMicrophone = () => {
    setEnabled(true);
    // Assuming getAudioDevices is a prop or context function to fetch audio devices
    // getAudioDevices();
  };

  if (error) {
    return (
      <div className={`flex items-center gap-2 text-red-600 text-sm ${className}`}>
        <FontAwesomeIcon icon={faExclamationTriangle} />
        <span>{error}</span>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={`flex items-center gap-2 text-gray-500 text-sm ${className}`}>
        <FontAwesomeIcon icon={faMicrophone} className="animate-pulse" />
        <span>Loading...</span>
      </div>
    );
  }

  if (audioDevices.length === 0) {
    return (
      <div className={`flex items-center gap-2 text-gray-500 text-sm ${className}`}>
        <FontAwesomeIcon icon={faMicrophone} />
        <span>No devices</span>
      </div>
    );
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <FontAwesomeIcon icon={faMicrophone} className="text-gray-600" />
      {!enabled ? (
        <button 
          onClick={handleEnableMicrophone} 
          className="text-blue-500 hover:underline"
        >
          Enable Microphone
        </button>
      ) : (
        <Select
          items={audioDevices.map(device => device.label)}
          placeholder="Select microphone"
          selected={selectedDevice?.label || null}
          setSelected={(label) => {
            // const device = audioDevices.find(d => d.label === label);
            // if (device) {
            //   selectDevice(device.deviceId);
            // }
          }}
        />
      )}
    </div>
  );
};

export default AudioDeviceSelector;
