{"name": "repd-admin-react", "version": "0.1.0", "private": true, "dependencies": {"@datadog/browser-logs": "^5.30.1", "@fortawesome/fontawesome-free": "^6.7.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@headlessui/react": "^1.7.19", "@heroicons/react": "^2.1.3", "@json2csv/plainjs": "^7.0.6", "@material-ui/core": "^4.12.4", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-slot": "^1.1.1", "@tailwindcss/postcss": "^4.0.1", "@tanstack/react-query": "^5.75.5", "@tanstack/react-table": "^8.20.6", "@testing-library/jest-dom": "^5.16.1", "@testing-library/react": "^12.1.2", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.4.0", "@types/node": "^16.11.21", "@types/react": "^17.0.38", "@types/react-dom": "^17.0.11", "axios": "^0.25.0", "chart.js": "^3.7.1", "class-variance-authority": "^0.7.1", "classnames": "^2.3.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "env-cmd": "^10.1.0", "framer-motion": "^12.0.6", "html2canvas": "^1.4.1", "json-as-xlsx": "^2.5.6", "jspdf": "^2.5.2", "lucide-react": "^0.474.0", "moment": "^2.30.1", "react": "^18.3.1", "react-chartjs-2": "^4.0.1", "react-csv": "^2.2.2", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-media-recorder": "^1.6.3", "react-modal": "^3.16.3", "react-router-dom": "^6.2.1", "react-scripts": "5.0.0", "react-video-thumbnail": "^0.1.3", "recharts": "^2.15.1", "sass": "^1.49.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "typescript": "^4.5.5", "web-vitals": "^2.1.4", "zustand": "^5.0.3"}, "scripts": {"start": "REACT_APP_DEPLOYMENT_ENV=local PORT=3001 react-scripts start", "start:staging": "env-cmd -f .env.development react-scripts start", "build-app": "TSC_COMPILE_ON_ERROR=true react-scripts build --force", "build": "env-cmd -f .env.staging npm run build-app", "build:prod": "env-cmd -f .env.prod npm run build-app --obfuscate", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@fortawesome/fontawesome": "^1.1.8", "@fortawesome/react-fontawesome": "^0.1.17", "@radix-ui/react-popover": "^1.1.5", "@types/react-csv": "^1.1.2", "@types/react-helmet": "^6.1.5", "@types/react-modal": "^3.16.3", "autoprefixer": "^10.4.2", "date-fns": "^4.1.0", "formik": "^2.2.9", "json-as-xlsx": "^2.5.6", "postcss": "^8.5.1", "react-day-picker": "^9.5.1", "react-helmet": "^6.1.0", "react-toastify": "^8.2.0", "tailwindcss": "^3.4.17", "typescript-plugin-css-modules": "^3.4.0", "yup": "^0.32.11", "yup-password": "^0.2.2"}}